import React, { useCallback, useRef, useEffect, useState } from 'react';
import { Node } from './Node';
import { Connection } from './Connection';
import { Toolbar } from './Toolbar';
import { MiniMap } from './MiniMap';
import { useMindMap } from '../hooks/useMindMap';
import { useTheme } from '../hooks/useTheme';
import { ChevronUp, ChevronDown, HelpCircle } from 'lucide-react';
import { SocialLinks } from './SocialLinks';

export const MindMap: React.FC = () => {
  const {
    data,
    viewport,
    setViewport,
    updateNode,
    setNodeQuery,
    clearNodeQuery,
    createChildNode,
    deleteNode,
    toggleNodeSearchGrounding,
    selectNode,
    newCanvas,
    exportData,
    importData,
    apiKey,
    setApiKey,
  } = useMindMap();

  const { theme, toggleTheme } = useTheme();

  const containerRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });
  const [isInstructionsExpanded, setIsInstructionsExpanded] = useState(false);

  // Pan handling
  const handleMouseDown = (e: React.MouseEvent) => {
    // Only start panning if clicking directly on the canvas background
    const target = e.target as HTMLElement;
    const isInteractiveElement = target.closest('.node-container') || 
                               target.closest('.toolbar') || 
                               target.closest('.no-pan') ||
                               target.closest('button') ||
                               target.closest('input') ||
                               target.closest('textarea');
    
    if (!isInteractiveElement) {
      e.preventDefault();
      e.stopPropagation();
      setIsPanning(true);
      setPanStart({ x: e.clientX - viewport.x, y: e.clientY - viewport.y });
      selectNode(''); // Deselect all nodes
      
      // Change cursor immediately
      if (containerRef.current) {
        containerRef.current.style.cursor = 'grabbing';
      }
    }
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isPanning) {
      e.preventDefault();
      const newX = e.clientX - panStart.x;
      const newY = e.clientY - panStart.y;
      
      setViewport({
        ...viewport,
        x: newX,
        y: newY,
      });
    }
  }, [isPanning, panStart, viewport, setViewport]);

  const handleMouseUp = useCallback(() => {
    if (isPanning) {
      setIsPanning(false);
      
      // Reset cursor
      if (containerRef.current) {
        containerRef.current.style.cursor = 'grab';
      }
    }
  }, [isPanning]);

  // Global mouse event listeners for panning
  useEffect(() => {
    if (isPanning) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('mouseleave', handleMouseUp); // Handle mouse leaving window
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('mouseleave', handleMouseUp);
      };
    }
  }, [isPanning, handleMouseMove, handleMouseUp]);

  // Handle zoom with mouse wheel and intercept pinch/ctrl+wheel to prevent browser zoom
  const handleWheel = (e: React.WheelEvent) => {
    // Prevent browser zoom on pinch/ctrl+wheel
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      e.stopPropagation();
    }
    // Check for modifier keys first
    const isCmdKey = e.metaKey; // Cmd key on Mac
    const isCtrlKey = e.ctrlKey; // Ctrl key on Windows/Linux
    const isShiftKey = e.shiftKey;

    // Prevent default only for the cases we handle
    if (isCmdKey || isCtrlKey || isShiftKey) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // Handle zoom with Cmd+Scroll (Mac) or Ctrl+Scroll (Windows/Linux)
    if (isCmdKey || isCtrlKey) {
      // Use a much higher multiplier for pinch/spread gestures from touchpad
      // (wheel events with ctrlKey/metaKey are pinch/spread on Mac/trackpad)
      const zoomFactor = 0.02; // Experimentally responsive; adjust if needed
      const newZoom = Math.max(0.1, Math.min(2, viewport.zoom - e.deltaY * zoomFactor));
      
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;
        
        const zoomRatio = newZoom / viewport.zoom;
        const newX = mouseX - (mouseX - viewport.x) * zoomRatio;
        const newY = mouseY - (mouseY - viewport.y) * zoomRatio;
        
        setViewport({ ...viewport, x: newX, y: newY, zoom: newZoom });
      }
    } 
    // Handle horizontal pan with Shift+Scroll
    else if (isShiftKey) {
      e.preventDefault();
      e.stopPropagation();
      const panSpeed = 1.5; // Slightly faster panning
      const newX = viewport.x - (e.deltaY || e.deltaX) * panSpeed;
      setViewport(prev => ({ ...prev, x: newX }));
    }
    // Default: two-finger scroll (no modifiers): pan horizontally and vertically
    else if (!isCmdKey && !isCtrlKey && !isShiftKey) {
      e.preventDefault();
      e.stopPropagation();
      // Use deltaX for horizontal pan (trackpad two-finger left/right)
      // Use deltaY for vertical pan (trackpad two-finger up/down)
      const panSpeed = 1.5; // Slightly faster vertical panning
      setViewport(prev => ({
        ...prev,
        x: prev.x - e.deltaX, // natural horizontal pan, no speed multiplier
        y: prev.y - e.deltaY * panSpeed,
      }));
    }
  };

  // --- Touchpad/Touch Pinch-Zoom Support ---
  const lastTouchDistance = useRef<number | null>(null);

  // Helper to get distance between two touches
  function getTouchDistance(touches: TouchList) {
    if (touches.length < 2) return 0;
    const dx = touches[0].clientX - touches[1].clientX;
    const dy = touches[0].clientY - touches[1].clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }

  // Touch event handlers for pinch/spread zoom
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Prevent browser-native pinch zoom (Safari/Mac/others)
    function gesturePreventDefault(e: Event) {
      e.preventDefault();
    }
    container.addEventListener('gesturestart', gesturePreventDefault);
    container.addEventListener('gesturechange', gesturePreventDefault);
    container.addEventListener('gestureend', gesturePreventDefault);

    // Prevent browser zoom on ctrl+wheel (Edge/Chrome/Firefox/Mac)
    function wheelPreventDefault(e: WheelEvent) {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
      }
    }
    container.addEventListener('wheel', wheelPreventDefault, { passive: false });

    function handleTouchStart(e: TouchEvent) {
      if (e.touches.length === 2) {
        lastTouchDistance.current = getTouchDistance(e.touches);
      }
    }

    function handleTouchMove(e: TouchEvent) {
      if (e.touches.length === 2 && lastTouchDistance.current !== null) {
        e.preventDefault();
        const newDistance = getTouchDistance(e.touches);
        const diff = newDistance - lastTouchDistance.current;
        // Pinch (fingers together, distance decreases): zoom out
        // Spread (fingers apart, distance increases): zoom in
        const zoomDelta = diff * 10; // much higher sensitivity
        const newZoom = Math.max(0.1, Math.min(2, viewport.zoom + zoomDelta));

        // Zoom centered at midpoint between touches
        if (container) {
          const rect = container.getBoundingClientRect();
          const midX = (e.touches[0].clientX + e.touches[1].clientX) / 2 - rect.left;
          const midY = (e.touches[0].clientY + e.touches[1].clientY) / 2 - rect.top;
          const zoomRatio = newZoom / viewport.zoom;
          const newX = midX - (midX - viewport.x) * zoomRatio;
          const newY = midY - (midY - viewport.y) * zoomRatio;

          setViewport(prev => ({ ...prev, x: newX, y: newY, zoom: newZoom }));
        }
        // Always update lastTouchDistance so small moves accumulate
        lastTouchDistance.current = newDistance;
      }
    }

    function handleTouchEnd(e: TouchEvent) {
      if (e.touches.length < 2) {
        lastTouchDistance.current = null;
      }
    }

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);
    container.addEventListener('touchcancel', handleTouchEnd);

    return () => {
      container.removeEventListener('gesturestart', gesturePreventDefault);
      container.removeEventListener('gesturechange', gesturePreventDefault);
      container.removeEventListener('gestureend', gesturePreventDefault);
      container.removeEventListener('wheel', wheelPreventDefault);
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
      container.removeEventListener('touchcancel', handleTouchEnd);
    };
  }, [viewport, setViewport]);

  // Toolbar handlers
  const handleZoomIn = () => {
    const newZoom = Math.min(2, viewport.zoom + 0.1);
    setViewport({ ...viewport, zoom: newZoom });
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(0.1, viewport.zoom - 0.1);
    setViewport({ ...viewport, zoom: newZoom });
  };

  const handleResetView = () => {
    setViewport({ x: 0, y: 0, zoom: 1 });
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        importData(file);
      }
    };
    input.click();
  };

  // Render connections
  const renderConnections = () => {
    const connections: React.ReactNode[] = [];
    Object.values(data.nodes).forEach((node) => {
      node.childIds.forEach((childId) => {
        const child = data.nodes[childId];
        if (child) {
          connections.push(
            <Connection
              key={`${node.id}-${childId}`}
              parent={node}
              child={child}
              viewport={viewport}
            />
          );
        }
      });
    });
    return connections;
  };

  return (
    <div className="w-full h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden relative transition-colors duration-200">
      <div className="fixed top-4 left-4 right-4 flex justify-between z-50">
        <Toolbar
          viewport={viewport}
          onZoomIn={handleZoomIn}
          onZoomOut={handleZoomOut}
          onResetView={handleResetView}
          onExport={exportData}
          onImport={handleImport}
          onNewCanvas={newCanvas}
          theme={theme}
          onToggleTheme={toggleTheme}
          apiKey={apiKey}
          onSetApiKey={setApiKey}
        />
        <SocialLinks />
      </div>

      <div
        ref={containerRef}
        className="w-full h-full cursor-grab select-none relative"
        onMouseDown={handleMouseDown}
        onWheel={handleWheel}
        onPointerDown={(e) => {
          const target = e.target as HTMLElement;
          // Don't capture pointer for interactive elements or their containers
          const isInteractiveElement =
            target.closest('.node-container') ||
            target.closest('button') ||
            target.closest('input') ||
            target.closest('textarea') ||
            target.closest('h3') ||  // For node titles
            target.closest('.flex-1'); // For the title container
          
          // Only capture pointer for non-interactive elements
          if (!isInteractiveElement && e.currentTarget.setPointerCapture) {
            e.currentTarget.setPointerCapture(e.pointerId);
          } else {
            // Prevent the drag behavior if clicking on interactive elements
            e.stopPropagation();
          }
        }}
        style={{
          cursor: isPanning ? 'grabbing' : 'grab',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          overflow: 'hidden',
          touchAction: 'none', // Prevent browser-native pinch zoom and pan for this area
        }}
      >
        {/* Canvas content with transform */}
        <div
          className="absolute inset-0 w-full h-full"
          style={{
            transform: `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`,
            transformOrigin: '0 0',
          }}
        >
          {/* Connections Layer - Behind nodes */}
          <div 
            className="absolute inset-0" 
            style={{ 
              zIndex: 1,
              pointerEvents: 'none' // Connections don't need pointer events
            }}
          >
            {renderConnections()}
          </div>

          {/* Nodes Layer - Above connections */}
          <div 
            className="absolute inset-0" 
            style={{ 
              zIndex: 2,
              pointerEvents: 'auto' // Explicitly enable pointer events for nodes
            }}
          >
            {Object.values(data.nodes).map((node) => (
              <Node
                key={node.id}
                node={node}
                nodes={data.nodes}
                onUpdateNode={updateNode}
                onSetNodeQuery={setNodeQuery}
                onClearNodeQuery={clearNodeQuery}
                onCreateChild={createChildNode}
                onDelete={deleteNode}
                onSelect={selectNode}
                onToggleNodeSearchGrounding={toggleNodeSearchGrounding}
                apiKey={apiKey}
                isRoot={node.id === data.rootNodeId}
              />
            ))}
          </div>
        </div>
      </div>

      <MiniMap
        nodes={data.nodes}
        viewport={viewport}
        onViewportChange={setViewport}
      />

      {/* Collapsible Instructions Panel */}
      <div className="fixed bottom-4 left-4 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 shadow-lg z-50 transition-all duration-200">
        {/* Header with toggle */}
        <div 
          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50/50 dark:hover:bg-gray-800/50 rounded-t-lg transition-colors"
          onClick={() => setIsInstructionsExpanded(!isInstructionsExpanded)}
        >
          <div className="flex items-center gap-2">
            <HelpCircle size={16} className="text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Quick Guide
            </span>
          </div>
          <button className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
            {isInstructionsExpanded ? <ChevronDown size={16} /> : <ChevronUp size={16} />}
          </button>
        </div>

        {/* Collapsible content */}
        <div className={`overflow-hidden transition-all duration-200 ${
          isInstructionsExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className="px-3 pb-3 border-t border-gray-200/50 dark:border-gray-700/50 pt-3">
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1.5 max-w-sm">
              <div className="flex items-start gap-2">
                <span className="text-blue-600 dark:text-blue-400 font-medium">•</span>
                <span>First add your <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline">Gemini API Key</a> in the toolbar</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-indigo-600 dark:text-indigo-400 font-medium">•</span>
                <span>Use + button to create child nodes</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-teal-600 dark:text-teal-400 font-medium">•</span>
                <span>Toggle 🔍 for search grounding per node</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="text-yellow-600 dark:text-yellow-400 font-medium">•</span>
                <span>Use + in toolbar to start a fresh canvas</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};